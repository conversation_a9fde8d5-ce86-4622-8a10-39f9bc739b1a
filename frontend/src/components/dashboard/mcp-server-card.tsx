'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Check, Zap, X } from 'lucide-react';
import { Co<PERSON>sioApp } from '@/types/composio';

interface MCPServerCardProps {
  app: ComposioApp;
  isConnected: boolean;
  isConnecting: boolean;
  onConnect: () => void;
  onDisconnect: () => void;
}

export function MCPServerCard({
  app,
  isConnected,
  isConnecting,
  onConnect,
  onDisconnect
}: MCPServerCardProps) {
  return (
    <motion.div
      whileHover={{
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      className="flex-shrink-0"
    >
      <Card
        className="w-48 h-16 cursor-pointer transition-all duration-200 hover:shadow-lg bg-muted/50 dark:bg-muted/30 border border-border"
        onClick={!isConnected && !isConnecting ? onConnect : undefined}
      >
        <CardContent className="p-3 h-full flex items-center justify-between">
          {/* Left: Icon + Text */}
          <div className="flex items-center gap-3 min-w-0 flex-1">
            {/* App Icon - Consistent 24x24 */}
            <div className="w-6 h-6 flex-shrink-0">
              {app.icon && app.icon.startsWith('http') ? (
                <img
                  src={app.icon}
                  alt={app.name}
                  className="w-6 h-6 rounded object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const fallback = target.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'flex';
                  }}
                />
              ) : null}

              {/* Fallback icon */}
              <div
                className="w-6 h-6 flex items-center justify-center bg-muted rounded text-muted-foreground"
                style={{ display: app.icon && app.icon.startsWith('http') ? 'none' : 'flex' }}
              >
                {app.icon && !app.icon.startsWith('http') ? (
                  <span className="text-sm">{app.icon}</span>
                ) : (
                  <Zap className="h-3 w-3" />
                )}
              </div>
            </div>

            {/* App Text - Better hierarchy */}
            <div className="min-w-0 flex-1">
              <div className="font-medium text-sm text-foreground truncate leading-none">
                {app.name}
              </div>
              <div className="text-xs text-muted-foreground leading-none">
                {app.tool_count} tools
              </div>
            </div>
          </div>

          {/* Right: Status/Actions */}
          <div className="flex items-center gap-2 flex-shrink-0 ml-3">
            {isConnected ? (
              <>
                {/* Connected indicator */}
                <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                  <Check className="h-3 w-3 text-white" />
                </div>
                {/* Disconnect button */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDisconnect();
                  }}
                  className="h-6 w-6 p-0 hover:bg-destructive/10 hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </Button>
              </>
            ) : (
              /* Connect button */
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onConnect();
                }}
                disabled={isConnecting}
                className="h-7 px-3 text-xs"
              >
                {isConnecting ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  "Connect"
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
