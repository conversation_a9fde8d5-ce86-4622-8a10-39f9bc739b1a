'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Check, Zap, X } from 'lucide-react';
import { ComposioApp } from '@/types/composio';

interface MCPServerCardProps {
  app: ComposioApp;
  isConnected: boolean;
  isConnecting: boolean;
  onConnect: () => void;
  onDisconnect: () => void;
}

export function MCPServerCard({
  app,
  isConnected,
  isConnecting,
  onConnect,
  onDisconnect
}: MCPServerCardProps) {
  return (
    <motion.div
      whileHover={{
        scale: 1.01,
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      className="flex-shrink-0 relative z-0 hover:z-10"
      style={{ width: '240px' }}
    >
      <Card
        className="h-12 cursor-pointer transition-all duration-200 hover:shadow-lg bg-muted/50 dark:bg-muted/30 border border-border hover:border-border/80"
        onClick={!isConnected && !isConnecting ? onConnect : undefined}
      >
        <CardContent className="p-3 h-full flex items-center gap-3">
          {/* Left Section: Icon + App Info */}
          <div className="flex items-center gap-3 flex-1 min-w-0">
            {/* App Icon */}
            <div className="flex-shrink-0">
              {app.icon.startsWith('http') ? (
                <img
                  src={app.icon}
                  alt={app.name}
                  className="w-8 h-8 rounded object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                    (e.target as HTMLImageElement).nextElementSibling?.classList.remove('hidden');
                  }}
                />
              ) : (
                <div className="w-8 h-8 flex items-center justify-center text-lg bg-primary/10 rounded">
                  {app.icon || <Zap className="h-4 w-4 text-primary" />}
                </div>
              )}
              {/* Fallback icon (hidden by default) */}
              <div className="hidden">
                <Zap className="h-4 w-4 text-primary" />
              </div>
            </div>

            {/* App Info */}
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-sm truncate text-foreground">
                {app.name}
              </h3>
              {app.tool_count && (
                <p className="text-xs text-muted-foreground leading-tight">
                  {app.tool_count} tools
                </p>
              )}
            </div>
          </div>

          {/* Right Section: Connection Status / Button */}
          <div className="flex-shrink-0">
            {isConnected ? (
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                  <Check className="h-3 w-3 text-white" />
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDisconnect();
                  }}
                  className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground hover:bg-muted/80"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ) : (
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  onConnect();
                }}
                disabled={isConnecting}
                className="h-7 px-3 text-xs"
              >
                {isConnecting ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  "Connect"
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
